const fs = require('fs');
const path = require('path');
const { getS3Instance, hasAWSCredentials, getFileUrl } = require('./storageHelper');

/**
 * Assemble uploaded chunks into a final file
 * @param {Object} uploadSession - Upload session containing chunk information
 * @returns {Object} - Result with final file URL and metadata
 */
const assembleChunks = async (uploadSession) => {
  const { uploadId, fileName, fileSize, fileType, uploadedChunks, totalChunks } = uploadSession;
  
  console.log(`[ChunkAssembler] Starting assembly for ${fileName} (${totalChunks} chunks)`);

  // Sort chunks by index to ensure correct order
  const sortedChunks = uploadedChunks
    .filter(chunk => chunk) // Remove empty slots
    .sort((a, b) => a.chunkIndex - b.chunkIndex);

  if (sortedChunks.length !== totalChunks) {
    throw new Error(`Missing chunks: expected ${totalChunks}, got ${sortedChunks.length}`);
  }

  // Debug: Log first few chunk paths to understand the URL structure
  console.log(`[ChunkAssembler] Sample chunk paths:`);
  sortedChunks.slice(0, 3).forEach((chunk, index) => {
    console.log(`  Chunk ${chunk.chunkIndex}: ${chunk.tempPath}`);
  });
  
  const timestamp = Date.now();
  const sanitizedName = fileName.replace(/[^a-zA-Z0-9.-]/g, '-');
  const finalFileName = `${timestamp}-${sanitizedName}`;
  
  if (hasAWSCredentials()) {
    return await assembleChunksS3(sortedChunks, finalFileName, fileType, uploadId);
  } else {
    return await assembleChunksLocal(sortedChunks, finalFileName, fileType, uploadId);
  }
};

/**
 * Assemble chunks for S3 storage using optimized copy operations
 */
const assembleChunksS3 = async (sortedChunks, finalFileName, fileType, uploadId) => {
  const s3 = getS3Instance();
  if (!s3) {
    throw new Error('S3 not configured');
  }

  console.log(`[ChunkAssembler] Assembling ${sortedChunks.length} chunks for S3 upload using optimized copy operations`);

  // For S3, we need to use multipart upload completion
  // First, create a multipart upload
  const s3Key = `uploads/content/${finalFileName}`;

  const createParams = {
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: s3Key,
    ContentType: fileType,
    Metadata: {
      originalName: finalFileName,
      uploadId: uploadId,
      assembledAt: Date.now().toString()
    }
  };

  let multipartUpload = null;

  try {
    multipartUpload = await s3.createMultipartUpload(createParams).promise();
    const uploadId_s3 = multipartUpload.UploadId;

    console.log(`[ChunkAssembler] Created S3 multipart upload: ${uploadId_s3}`);

    // Set timeout for the entire assembly process (10 minutes max)
    const assemblyTimeout = setTimeout(() => {
      console.error(`[ChunkAssembler] Assembly timeout after 10 minutes for ${sortedChunks.length} chunks`);
      throw new Error('Chunk assembly timeout - process took too long');
    }, 600000); // 10 minutes

    // Process chunks in batches to optimize memory usage and performance
    const BATCH_SIZE = 5; // Process 5 chunks at a time
    const parts = [];

    for (let i = 0; i < sortedChunks.length; i += BATCH_SIZE) {
      const batch = sortedChunks.slice(i, i + BATCH_SIZE);
      console.log(`[ChunkAssembler] Processing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(sortedChunks.length / BATCH_SIZE)} (chunks ${i + 1}-${Math.min(i + BATCH_SIZE, sortedChunks.length)})`);

      const batchPromises = batch.map(async (chunk, batchIndex) => {
        const index = i + batchIndex;
        const partNumber = index + 1;

        if (chunk.tempPath.startsWith('https://') || chunk.tempPath.includes('amazonaws.com')) {
          // Chunk is already in S3 - use copyPart for efficiency
          const url = new URL(chunk.tempPath);
          const chunkKey = url.pathname.substring(1); // Remove leading slash

          console.log(`[ChunkAssembler] Copying chunk ${index + 1} from S3 key: ${chunkKey}`);

          const copyParams = {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: s3Key,
            PartNumber: partNumber,
            UploadId: uploadId_s3,
            CopySource: `${process.env.AWS_BUCKET_NAME}/${chunkKey}`
          };

          try {
            const result = await s3.uploadPartCopy(copyParams).promise();
            console.log(`[ChunkAssembler] Copied part ${partNumber}/${sortedChunks.length} from S3`);

            return {
              ETag: result.CopyPartResult.ETag,
              PartNumber: partNumber
            };
          } catch (error) {
            console.error(`[ChunkAssembler] Error copying chunk ${index + 1} from S3:`, error.code, error.message);
            console.error(`[ChunkAssembler] Chunk URL: ${chunk.tempPath}`);
            console.error(`[ChunkAssembler] Extracted key: ${chunkKey}`);
            console.error(`[ChunkAssembler] Copy source: ${process.env.AWS_BUCKET_NAME}/${chunkKey}`);

            // Fallback to download and upload if copy fails
            console.log(`[ChunkAssembler] Falling back to download/upload for chunk ${index + 1}`);
            return await downloadAndUploadChunk(s3, chunk, index, s3Key, partNumber, uploadId_s3);
          }
        } else {
          // Chunk is local file - upload directly
          const chunkData = fs.readFileSync(chunk.tempPath);
          console.log(`[ChunkAssembler] Uploading local chunk ${index + 1} (${chunkData.length} bytes)`);

          const uploadPartParams = {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: s3Key,
            PartNumber: partNumber,
            UploadId: uploadId_s3,
            Body: chunkData
          };

          const result = await s3.uploadPart(uploadPartParams).promise();
          console.log(`[ChunkAssembler] Uploaded part ${partNumber}/${sortedChunks.length}`);

          return {
            ETag: result.ETag,
            PartNumber: partNumber
          };
        }
      });

      const batchParts = await Promise.all(batchPromises);
      parts.push(...batchParts);

      // Small delay between batches to prevent overwhelming S3
      if (i + BATCH_SIZE < sortedChunks.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Clear the timeout since we completed successfully
    clearTimeout(assemblyTimeout);

    // Complete the multipart upload
    const completeParams = {
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: s3Key,
      UploadId: uploadId_s3,
      MultipartUpload: {
        Parts: parts
      }
    };

    const finalResult = await s3.completeMultipartUpload(completeParams).promise();

    // Clean up temporary chunks
    await cleanupTempChunks(sortedChunks);

    console.log(`[ChunkAssembler] S3 assembly completed: ${finalResult.Location}`);

    return {
      fileUrl: finalResult.Location,
      bucket: finalResult.Bucket,
      key: finalResult.Key,
      etag: finalResult.ETag
    };

  } catch (error) {
    console.error('[ChunkAssembler] S3 assembly error:', error);

    // Clear timeout on error
    if (typeof assemblyTimeout !== 'undefined') {
      clearTimeout(assemblyTimeout);
    }

    // Clean up failed multipart upload if it exists
    if (multipartUpload && multipartUpload.UploadId) {
      try {
        await s3.abortMultipartUpload({
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: s3Key,
          UploadId: multipartUpload.UploadId
        }).promise();
        console.log('[ChunkAssembler] Aborted failed multipart upload');
      } catch (abortError) {
        console.error('[ChunkAssembler] Error aborting multipart upload:', abortError);
      }
    }

    throw error;
  }
};

/**
 * Fallback function to download and upload chunk if copy fails
 */
const downloadAndUploadChunk = async (s3, chunk, index, s3Key, partNumber, uploadId_s3) => {
  const url = new URL(chunk.tempPath);
  const chunkKey = url.pathname.substring(1);

  const getParams = {
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: chunkKey
  };

  const chunkObject = await s3.getObject(getParams).promise();
  const chunkData = chunkObject.Body;
  console.log(`[ChunkAssembler] Downloaded chunk ${index + 1} as fallback (${chunkData.length} bytes)`);

  const uploadPartParams = {
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: s3Key,
    PartNumber: partNumber,
    UploadId: uploadId_s3,
    Body: chunkData
  };

  const result = await s3.uploadPart(uploadPartParams).promise();
  console.log(`[ChunkAssembler] Uploaded part ${partNumber} via fallback method`);

  return {
    ETag: result.ETag,
    PartNumber: partNumber
  };
};

/**
 * Assemble chunks for local storage
 */
const assembleChunksLocal = async (sortedChunks, finalFileName, fileType, uploadId) => {
  console.log(`[ChunkAssembler] Assembling ${sortedChunks.length} chunks for local storage`);
  
  const uploadsDir = './uploads';
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }
  
  const finalFilePath = path.join(uploadsDir, finalFileName);
  const writeStream = fs.createWriteStream(finalFilePath);
  
  try {
    // Write chunks in order
    for (let i = 0; i < sortedChunks.length; i++) {
      const chunk = sortedChunks[i];
      console.log(`[ChunkAssembler] Writing chunk ${i + 1}/${sortedChunks.length}`);
      
      const chunkData = fs.readFileSync(chunk.tempPath);
      writeStream.write(chunkData);
    }
    
    writeStream.end();
    
    // Wait for write to complete
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });
    
    // Clean up temporary chunks
    await cleanupTempChunks(sortedChunks);
    
    console.log(`[ChunkAssembler] Local assembly completed: ${finalFilePath}`);
    
    return {
      fileUrl: `/uploads/${finalFileName}`,
      filename: finalFileName,
      path: finalFilePath
    };
    
  } catch (error) {
    console.error('[ChunkAssembler] Local assembly error:', error);
    
    // Clean up partial file
    if (fs.existsSync(finalFilePath)) {
      fs.unlinkSync(finalFilePath);
    }
    
    throw error;
  }
};

/**
 * Clean up temporary chunk files
 */
const cleanupTempChunks = async (chunks) => {
  console.log(`[ChunkAssembler] Cleaning up ${chunks.length} temporary chunks`);
  
  const s3 = getS3Instance();
  
  for (const chunk of chunks) {
    try {
      if (chunk.tempPath.startsWith('https://') || chunk.tempPath.includes('amazonaws.com')) {
        // S3 chunk - delete from S3
        if (s3) {
          const url = new URL(chunk.tempPath);
          const chunkKey = url.pathname.substring(1); // Remove leading slash
          await s3.deleteObject({
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: chunkKey
          }).promise();
          console.log(`[ChunkAssembler] Deleted S3 chunk: ${chunkKey}`);
        }
      } else {
        // Local chunk - delete file
        if (fs.existsSync(chunk.tempPath)) {
          fs.unlinkSync(chunk.tempPath);
          console.log(`[ChunkAssembler] Deleted local chunk: ${chunk.tempPath}`);
        }
      }
    } catch (error) {
      console.error(`[ChunkAssembler] Error cleaning up chunk ${chunk.tempPath}:`, error);
      // Continue with other chunks even if one fails
    }
  }
};

module.exports = {
  assembleChunks,
  cleanupTempChunks
};
