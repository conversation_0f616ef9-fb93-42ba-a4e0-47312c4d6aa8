/**
 * S3 Large Upload Verification Script
 * This script verifies that your S3 bucket is properly configured for large file uploads
 */

const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuration
const BUCKET_NAME = process.env.AWS_BUCKET_NAME;
const REGION = process.env.AWS_REGION;
const TEST_FILE_SIZE = 100 * 1024 * 1024; // 100MB test file

// Initialize S3 with optimized settings
const s3 = new AWS.S3({
  apiVersion: '2006-03-01',
  region: REGION,
  signatureVersion: 'v4',
  maxRetries: 3,
  retryDelayOptions: {
    customBackoff: function(retryCount) {
      return Math.pow(2, retryCount) * 1000;
    }
  },
  httpOptions: {
    timeout: 3600000, // 1 hour timeout
    agent: false
  }
});

/**
 * Check if AWS credentials are configured
 */
function checkCredentials() {
  console.log('🔐 Checking AWS Credentials...');
  
  if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
    console.error('❌ AWS credentials not found in environment variables');
    return false;
  }
  
  if (!BUCKET_NAME || !REGION) {
    console.error('❌ AWS_BUCKET_NAME or AWS_REGION not configured');
    return false;
  }
  
  console.log('✅ AWS credentials configured');
  console.log(`   Bucket: ${BUCKET_NAME}`);
  console.log(`   Region: ${REGION}`);
  return true;
}

/**
 * Check bucket existence and permissions
 */
async function checkBucketAccess() {
  console.log('\n🪣 Checking S3 Bucket Access...');
  
  try {
    // Check if bucket exists and we have access
    await s3.headBucket({ Bucket: BUCKET_NAME }).promise();
    console.log('✅ Bucket exists and accessible');
    
    // Check bucket location
    const location = await s3.getBucketLocation({ Bucket: BUCKET_NAME }).promise();
    console.log(`   Bucket location: ${location.LocationConstraint || 'us-east-1'}`);
    
    return true;
  } catch (error) {
    console.error('❌ Bucket access failed:', error.message);
    return false;
  }
}

/**
 * Check bucket CORS configuration
 */
async function checkCORSConfiguration() {
  console.log('\n🌐 Checking CORS Configuration...');
  
  try {
    const cors = await s3.getBucketCors({ Bucket: BUCKET_NAME }).promise();
    console.log('✅ CORS configuration found');
    
    // Check if CORS allows large uploads
    const hasUploadRule = cors.CORSRules.some(rule => 
      rule.AllowedMethods.includes('PUT') || rule.AllowedMethods.includes('POST')
    );
    
    if (hasUploadRule) {
      console.log('✅ CORS allows upload methods');
    } else {
      console.log('⚠️  CORS may not allow upload methods');
    }
    
    return true;
  } catch (error) {
    if (error.code === 'NoSuchCORSConfiguration') {
      console.log('⚠️  No CORS configuration found (may be okay for server-side uploads)');
      return true;
    }
    console.error('❌ CORS check failed:', error.message);
    return false;
  }
}

/**
 * Test multipart upload capability
 */
async function testMultipartUpload() {
  console.log('\n📤 Testing Multipart Upload Capability...');
  
  try {
    const testKey = `test-uploads/multipart-test-${Date.now()}.bin`;
    
    // Initiate multipart upload
    const multipart = await s3.createMultipartUpload({
      Bucket: BUCKET_NAME,
      Key: testKey,
      ContentType: 'application/octet-stream'
    }).promise();
    
    console.log('✅ Multipart upload initiated');
    console.log(`   Upload ID: ${multipart.UploadId}`);
    
    // Abort the test upload (cleanup)
    await s3.abortMultipartUpload({
      Bucket: BUCKET_NAME,
      Key: testKey,
      UploadId: multipart.UploadId
    }).promise();
    
    console.log('✅ Multipart upload test completed');
    return true;
  } catch (error) {
    console.error('❌ Multipart upload test failed:', error.message);
    return false;
  }
}

/**
 * Create a test file for upload testing
 */
function createTestFile() {
  const testFilePath = path.join(__dirname, 'test-large-file.bin');
  const buffer = Buffer.alloc(TEST_FILE_SIZE, 'A');
  fs.writeFileSync(testFilePath, buffer);
  return testFilePath;
}

/**
 * Test actual large file upload
 */
async function testLargeFileUpload() {
  console.log('\n📁 Testing Large File Upload...');
  console.log(`   Creating ${Math.round(TEST_FILE_SIZE / (1024 * 1024))}MB test file...`);
  
  let testFilePath;
  try {
    testFilePath = createTestFile();
    const testKey = `test-uploads/large-file-test-${Date.now()}.bin`;
    
    console.log('   Starting upload...');
    const startTime = Date.now();
    
    const uploadParams = {
      Bucket: BUCKET_NAME,
      Key: testKey,
      Body: fs.createReadStream(testFilePath),
      ContentType: 'application/octet-stream'
    };
    
    const managedUpload = s3.upload(uploadParams, {
      partSize: 10 * 1024 * 1024, // 10MB parts
      queueSize: 1
    });
    
    // Track progress
    managedUpload.on('httpUploadProgress', (progress) => {
      const percent = Math.round((progress.loaded / progress.total) * 100);
      process.stdout.write(`\r   Upload progress: ${percent}%`);
    });
    
    const result = await managedUpload.promise();
    const uploadTime = Math.round((Date.now() - startTime) / 1000);
    
    console.log(`\n✅ Large file upload successful in ${uploadTime}s`);
    console.log(`   Location: ${result.Location}`);
    
    // Cleanup - delete the test file from S3
    await s3.deleteObject({ Bucket: BUCKET_NAME, Key: testKey }).promise();
    console.log('✅ Test file cleaned up from S3');
    
    return true;
  } catch (error) {
    console.error('\n❌ Large file upload failed:', error.message);
    return false;
  } finally {
    // Cleanup local test file
    if (testFilePath && fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      console.log('✅ Local test file cleaned up');
    }
  }
}

/**
 * Check bucket policy for large uploads
 */
async function checkBucketPolicy() {
  console.log('\n📋 Checking Bucket Policy...');
  
  try {
    const policy = await s3.getBucketPolicy({ Bucket: BUCKET_NAME }).promise();
    console.log('✅ Bucket policy found');
    
    const policyDoc = JSON.parse(policy.Policy);
    console.log('   Policy statements:', policyDoc.Statement.length);
    
    return true;
  } catch (error) {
    if (error.code === 'NoSuchBucketPolicy') {
      console.log('⚠️  No bucket policy found (using default permissions)');
      return true;
    }
    console.error('❌ Bucket policy check failed:', error.message);
    return false;
  }
}

/**
 * Main verification function
 */
async function verifyS3Configuration() {
  console.log('🚀 S3 Large Upload Configuration Verification\n');
  console.log('=' .repeat(50));
  
  const checks = [
    checkCredentials,
    checkBucketAccess,
    checkCORSConfiguration,
    checkBucketPolicy,
    testMultipartUpload,
    testLargeFileUpload
  ];
  
  let passedChecks = 0;
  
  for (const check of checks) {
    try {
      const result = await check();
      if (result) passedChecks++;
    } catch (error) {
      console.error(`❌ Check failed: ${error.message}`);
    }
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log(`📊 Verification Summary: ${passedChecks}/${checks.length} checks passed`);
  
  if (passedChecks === checks.length) {
    console.log('🎉 Your S3 bucket is properly configured for large file uploads!');
  } else {
    console.log('⚠️  Some issues were found. Please review the failed checks above.');
  }
}

// Run verification if script is executed directly
if (require.main === module) {
  verifyS3Configuration().catch(console.error);
}

module.exports = {
  verifyS3Configuration,
  checkCredentials,
  checkBucketAccess,
  testMultipartUpload,
  testLargeFileUpload
};
